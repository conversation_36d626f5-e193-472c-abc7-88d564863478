# SPA Issue with Iframe Plugin - Research Findings

## Problem Description

When accessing the plugin URL `http://0.0.0.0:8080/plugins/af3-demo` directly (via refresh or direct URL entry), it returns a 404 error. However, when navigating to the same URL through the Airflow UI navigation menu, it works correctly.

## Root Cause Analysis

### The Issue: Route Conflict with Another Plugin

The problem is caused by a **route conflict** between the iframe plugin system and another plugin that's registering a FastAPI app with the `/plugins` URL prefix.

### Investigation Steps

1. **Direct URL Access Test**:
   ```bash
   curl -v "http://0.0.0.0:8080/plugins/af3-demo"
   # Result: HTTP/1.1 404 Not Found {"detail":"Not Found"}
   ```

2. **Main UI Access Test**:
   ```bash
   curl -v "http://0.0.0.0:8080/"
   # Result: HTTP/1.1 200 OK (serves main UI index.html)
   ```

3. **Plugin Registry Analysis**:
   ```bash
   curl "http://0.0.0.0:8080/api/v2/plugins"
   ```

   **Key Finding**: Two plugins are registered:
   - `af3_demo_plugin`: FastAPI app at `/af3-demo` ✅
   - `markdown_view_plugin`: FastAPI app at `/plugins` ❌ **CONFLICT!**

### Technical Explanation

#### Expected Behavior (SPA Routing)
1. User accesses `/plugins/af3-demo`
2. Server's catch-all route `@app.get("/{rest_of_path:path}")` serves main UI `index.html`
3. React Router loads and handles client-side routing to `plugins/:slug/*`
4. `PluginHost` component renders iframe with plugin content

#### Actual Behavior (Route Conflict)
1. User accesses `/plugins/af3-demo`
2. **markdown_view_plugin's FastAPI app** (mounted at `/plugins`) intercepts the request
3. Markdown plugin doesn't have a route for `/af3-demo`, returns 404
4. Request never reaches the main UI's catch-all route

### Code Analysis

#### Route Registration Order (from `airflow-core/src/airflow/api_fastapi/app.py`):
```python
def create_app(apps: str = "all") -> FastAPI:
    # ...
    if "core" in apps_list or "all" in apps_list:
        init_plugins(app)          # Plugins mounted first
        init_auth_manager(app)
        init_flask_plugins(app)
        init_views(app)           # Core views (including catch-all) mounted last
```

#### Plugin Mounting (from `init_plugins`):
```python
for subapp_dict in cast("list", plugins_manager.fastapi_apps):
    url_prefix = subapp_dict.get("url_prefix")
    app.mount(url_prefix, subapp)  # markdown plugin mounts at "/plugins"
```

#### Catch-all Route (from `init_views`):
```python
@app.get("/{rest_of_path:path}", response_class=HTMLResponse, include_in_schema=False)
def webapp(request: Request, rest_of_path: str):
    return templates.TemplateResponse("/index.html", ...)
```

### Why Navigation Works vs Direct Access Fails

- **Navigation**: Client-side routing within already-loaded React app
- **Direct Access**: Server-side request that gets intercepted by conflicting plugin

## Solutions

### Option 1: Change Conflicting Plugin URL Prefix (Recommended)
Modify the `markdown_view_plugin` to use a different URL prefix (e.g., `/markdown-view` instead of `/plugins`).

### Option 2: Implement Fallback in Conflicting Plugin
Add a catch-all route in the markdown plugin that forwards unmatched requests to the main UI.

### Option 3: Change Iframe Plugin URL Pattern
Use a different URL pattern for iframe plugins (e.g., `/ui-plugins/:slug` instead of `/plugins/:slug`).

### Option 4: Route Priority Management
Implement a system to ensure the main UI's catch-all route has lower priority than specific plugin routes.

## Recommended Fix

**Immediate**: Change the `markdown_view_plugin` URL prefix from `/plugins` to `/markdown-view` or similar.

**Long-term**: Establish plugin URL prefix conventions to prevent conflicts:
- Reserve `/plugins/*` for the main UI's plugin routing system
- Use descriptive prefixes for plugin FastAPI apps (e.g., `/plugin-name/*`)

## Files Involved

- `airflow-core/src/airflow/api_fastapi/core_api/app.py` - Main app routing
- `airflow-core/src/airflow/api_fastapi/app.py` - Plugin mounting logic
- `airflow-core/src/airflow/ui/src/router.tsx` - Client-side routing
- `airflow-core/src/airflow/ui/src/components/PluginHost.tsx` - Plugin iframe rendering
- Plugin files with conflicting URL prefixes

## Impact

This issue affects all iframe-based plugins when there's a URL prefix conflict. It breaks the fundamental SPA routing behavior expected by users when they refresh or directly access plugin URLs.