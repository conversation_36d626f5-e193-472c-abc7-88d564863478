{"defaultToGraphView": "그래프 뷰 기본 보기", "defaultToGridView": "그리드 뷰 기본 보기", "logout": "로그아웃", "switchToDarkMode": "다크 모드로 전환", "switchToLightMode": "라이트 모드로 전환", "timezone": "시간대", "user": "사용자", "language": {"chinese": "중국어 번체", "english": "영어", "korean": "한국어", "german": "독일어", "select": "언어 선택"}, "nav": {"home": "홈", "assets": "에셋", "browse": "탐색", "admin": "관리자", "docs": "문서", "plugins": "플러그인"}, "browse": {"auditLog": "감사 로그", "xcoms": "XComs"}, "admin": {"Variables": "변수들", "Pools": "Pools", "Providers": "제공자들", "Plugins": "플러그인들", "Connections": "연결들", "Config": "설정"}, "timeRange": {"duration": "지속 시간", "lastHour": "지난 1시간", "last12Hours": "지난 12 시간", "last24Hours": "지난 24 시간", "pastWeek": "지난 주"}, "docs": {"documentation": "문서", "githubRepo": "GitHub 저장소", "restApiReference": "REST API 참조"}, "states": {"queued": "대기 중", "running": "실행 중", "success": "성공", "failed": "실패", "skipped": "건너뜀", "removed": "제거됨", "scheduled": "예약됨", "restarting": "다시 시작 중", "up_for_retry": "재시도 대기 중", "up_for_reschedule": "재예약 대기 중", "upstream_failed": "업스트림 실패", "deferred": "연기됨", "no_status": "상태 없음"}, "dagRun_one": "Dag 실행", "dagRun_other": "Dag 실행들", "taskInstance_one": "작업 인스턴스", "taskInstance_other": "작업 인스턴스들", "assetEvent_one": "에셋 이벤트", "assetEvent_other": "에셋 이벤트들", "triggered": "트리거됨", "pools": {"open": "열림", "running": "실행 중", "queued": "대기 중", "scheduled": "예약됨", "deferred": "연기됨", "pools_one": "pool", "pools_other": "pools"}}